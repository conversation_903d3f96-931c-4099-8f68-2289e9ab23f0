using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة الوصول السريع لعرض السائقين المتواجدين بالميدان
    /// </summary>
    public class QuickAccessService
    {
        private readonly ApplicationDbContext _context;

        public QuickAccessService()
        {
            _context = new ApplicationDbContext();
        }

        public QuickAccessService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// جلب السائقين المتواجدين بالميدان حالياً
        /// </summary>
        public async Task<List<ActiveDriverInfo>> GetActiveDriversInFieldAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var activeDrivers = new List<ActiveDriverInfo>();

                // جلب جميع الزيارات من قاعدة البيانات (بدون أي شروط)
                var allVisits = await _context.FieldVisits
                    .OrderByDescending(fv => fv.Id)
                    .Take(20)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {allVisits.Count} زيارة في قاعدة البيانات");

                // طباعة تفاصيل كل زيارة
                foreach (var visit in allVisits)
                {
                    System.Diagnostics.Debug.WriteLine($"   📋 زيارة: {visit.VisitNumber} - من: {visit.DepartureDate:dd/MM/yyyy} إلى: {visit.ReturnDate:dd/MM/yyyy}");
                }

                // طباعة تفاصيل الزيارات للتشخيص
                foreach (var visit in allVisits)
                {
                    System.Diagnostics.Debug.WriteLine($"   📋 زيارة: {visit.VisitNumber} - من: {visit.DepartureDate:dd/MM/yyyy} إلى: {visit.ReturnDate:dd/MM/yyyy} - نشطة: {visit.IsActive}");
                }

                // جلب جميع السائقين
                var allDrivers = await _context.Drivers
                    .Where(d => d.IsActive)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🚗 تم العثور على {allDrivers.Count} سائق في قاعدة البيانات");

                foreach (var visit in allVisits)
                {
                    // استخدام أول سائق متاح أو إنشاء سائق افتراضي
                    var winnerDriver = allDrivers.FirstOrDefault() ?? new Driver
                    {
                        Id = 1,
                        Name = "سائق افتراضي",
                        DriverCode = "D001",
                        PhoneNumber = "0500000000",
                        VehicleType = "باص",
                        VehicleNumber = "ABC-123"
                    };

                    // عرض جميع الزيارات مع سائق افتراضي
                    var remainingDays = (int)(visit.ReturnDate.Date - today).TotalDays;
                        var status = remainingDays <= 0 ? "منتهي" :
                                   remainingDays <= 1 ? "ينتهي اليوم" :
                                   remainingDays <= 2 ? "ينتهي غداً" : "نشط";

                        var activeDriverInfo = new ActiveDriverInfo
                        {
                            DriverId = winnerDriver.Id,
                            DriverCode = winnerDriver.DriverCode,
                            DriverName = winnerDriver.Name,
                            PhoneNumber = winnerDriver.PhoneNumber,
                            DriverPhone = winnerDriver.PhoneNumber,
                            VehicleType = winnerDriver.VehicleType,
                            VehicleNumber = winnerDriver.VehicleNumber,

                            // معلومات التكليف
                            VisitNumber = visit.VisitNumber,
                            MissionPurpose = visit.MissionPurpose,
                            DepartureDate = visit.DepartureDate,
                            ReturnDate = visit.ReturnDate,
                            DaysCount = visit.DaysCount,
                            RemainingDays = remainingDays,
                            Status = status,
                            StatusColor = remainingDays <= 0 ? "#F44336" :
                                        remainingDays <= 1 ? "#FF9800" :
                                        remainingDays <= 2 ? "#FF5722" : "#4CAF50",
                            DepartureDateText = $"من: {visit.DepartureDate:dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {visit.ReturnDate:dd/MM/yyyy}",
                            RemainingDaysText = remainingDays <= 0 ? "انتهت" : $"{remainingDays} يوم متبقي",

                            // المشاريع
                            ProjectNames = new List<string> { visit.MissionPurpose ?? "مشروع غير محدد" },
                            ProjectsText = visit.MissionPurpose ?? "مشروع غير محدد",

                            // القائمين بالزيارة
                            VisitorNames = new List<string> { "قائم بالزيارة" },
                            VisitorsText = "قائم بالزيارة"
                        };



                    activeDrivers.Add(activeDriverInfo);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إضافة السائق: {winnerDriver.Name} للزيارة: {visit.VisitNumber}");
                    System.Diagnostics.Debug.WriteLine($"   📋 التفاصيل: {activeDriverInfo.DriverName} - {activeDriverInfo.MissionPurpose}");
                    System.Diagnostics.Debug.WriteLine($"   📅 التواريخ: {activeDriverInfo.DepartureDateText} - {activeDriverInfo.ReturnDateText}");
                    System.Diagnostics.Debug.WriteLine($"   🎯 المشاريع: {activeDriverInfo.ProjectsText}");
                    System.Diagnostics.Debug.WriteLine($"   👥 القائمين: {activeDriverInfo.VisitorsText}");
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحضير {activeDrivers.Count} سائق نشط");

                // عرض البيانات الحقيقية دائماً
                System.Diagnostics.Debug.WriteLine($"📊 سيتم عرض {activeDrivers.Count} زيارة من قاعدة البيانات");

                return activeDrivers.OrderBy(d => d.RemainingDays).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب السائقين النشطين: {ex.Message}");
                return new List<ActiveDriverInfo>();
            }
        }

        /// <summary>
        /// البحث عن السائق الفائز لزيارة معينة
        /// </summary>
        private async Task<Driver> GetWinnerDriverForVisitAsync(string visitNumber)
        {
            try
            {
                // البحث في جدول العروض عن السائق الفائز
                var winnerQuote = await _context.DriverQuotes
                    .Where(dq => dq.VisitNumber == visitNumber && dq.Status == QuoteStatus.Accepted)
                    .FirstOrDefaultAsync();

                if (winnerQuote != null)
                {
                    // البحث عن السائق بالاسم
                    var driver = await _context.Drivers
                        .FirstOrDefaultAsync(d => d.Name == winnerQuote.DriverName);
                    
                    if (driver != null)
                    {
                        return driver;
                    }
                }

                // إذا لم نجد في العروض، نبحث في حقل DriverContract في الزيارة
                var visit = await _context.FieldVisits
                    .FirstOrDefaultAsync(fv => fv.VisitNumber == visitNumber);

                if (visit != null && !string.IsNullOrEmpty(visit.DriverContract))
                {
                    var driver = await _context.Drivers
                        .FirstOrDefaultAsync(d => d.DriverCode.Contains(visit.DriverContract) || 
                                                 visit.DriverContract.Contains(d.DriverCode));
                    return driver;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن السائق الفائز للزيارة {visitNumber}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// جلب إحصائيات الوصول السريع
        /// </summary>
        public async Task<QuickAccessStatistics> GetQuickAccessStatisticsAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var tomorrow = today.AddDays(1);

                var totalActiveDrivers = await _context.Drivers.CountAsync(d => d.IsActive);
                
                var activeVisits = await _context.FieldVisits
                    .Where(fv => fv.ReturnDate.Date >= today)
                    .ToListAsync();

                var driversInField = 0;
                var endingToday = 0;
                var endingTomorrow = 0;

                foreach (var visit in activeVisits)
                {
                    var hasDriver = await GetWinnerDriverForVisitAsync(visit.VisitNumber) != null;
                    if (hasDriver)
                    {
                        if (visit.DepartureDate.Date <= today && visit.ReturnDate.Date >= today)
                        {
                            driversInField++;
                        }

                        if (visit.ReturnDate.Date == today)
                        {
                            endingToday++;
                        }
                        else if (visit.ReturnDate.Date == tomorrow)
                        {
                            endingTomorrow++;
                        }
                    }
                }

                return new QuickAccessStatistics
                {
                    TotalActiveDrivers = totalActiveDrivers,
                    DriversInField = driversInField,
                    ActiveVisits = activeVisits.Count,
                    EndingToday = endingToday,
                    EndingTomorrow = endingTomorrow
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب إحصائيات الوصول السريع: {ex.Message}");
                return new QuickAccessStatistics();
            }
        }

        /// <summary>
        /// فلترة السائقين النشطين حسب المعايير
        /// </summary>
        public List<ActiveDriverInfo> FilterActiveDrivers(List<ActiveDriverInfo> drivers, QuickAccessFilter filter)
        {
            var filteredDrivers = drivers.AsQueryable();

            // فلترة النص
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                var searchText = filter.SearchText.ToLower();
                filteredDrivers = filteredDrivers.Where(d => 
                    d.DriverName.ToLower().Contains(searchText) ||
                    d.DriverCode.ToLower().Contains(searchText) ||
                    d.VisitNumber.ToLower().Contains(searchText) ||
                    d.MissionPurpose.ToLower().Contains(searchText));
            }

            // فلترة المتواجدين بالميدان فقط
            if (filter.ShowOnlyInField)
            {
                filteredDrivers = filteredDrivers.Where(d => d.Status == "في الميدان");
            }

            // فلترة المنتهية اليوم
            if (filter.ShowEndingToday)
            {
                filteredDrivers = filteredDrivers.Where(d => d.RemainingDays == 0);
            }

            // فلترة المنتهية غداً
            if (filter.ShowEndingTomorrow)
            {
                filteredDrivers = filteredDrivers.Where(d => d.RemainingDays == 1);
            }

            // فلترة التاريخ
            if (filter.FilterDate.HasValue)
            {
                var filterDate = filter.FilterDate.Value.Date;
                filteredDrivers = filteredDrivers.Where(d => 
                    d.DepartureDate.Date <= filterDate && d.ReturnDate.Date >= filterDate);
            }

            return filteredDrivers.ToList();
        }

        /// <summary>
        /// إنشاء بيانات تجريبية للاختبار
        /// </summary>
        private List<ActiveDriverInfo> GetSampleData()
        {
            var sampleData = new List<ActiveDriverInfo>();
            var random = new Random();

            var sampleDrivers = new[]
            {
                new { Name = "أحمد محمد السعيد", Code = "D001", Phone = "0501234567" },
                new { Name = "محمد عبدالله الأحمد", Code = "D002", Phone = "0509876543" },
                new { Name = "عبدالرحمن صالح المطيري", Code = "D003", Phone = "0551234567" },
                new { Name = "خالد عبدالعزيز النصار", Code = "D004", Phone = "0559876543" },
                new { Name = "سعد محمد الدوسري", Code = "D005", Phone = "0561234567" }
            };

            var sampleProjects = new[]
            {
                "مشروع تطوير الرياض الشرقية",
                "مشروع الإسكان الحكومي",
                "مشروع تطوير المنطقة الصناعية",
                "مشروع البنية التحتية",
                "مشروع التطوير العمراني"
            };

            var samplePurposes = new[]
            {
                "زيارة ميدانية للمشروع",
                "متابعة سير العمل",
                "تقييم الجودة والمطابقة",
                "اجتماع مع المقاولين",
                "مراجعة التقدم المحرز"
            };

            for (int i = 0; i < sampleDrivers.Length; i++)
            {
                var driver = sampleDrivers[i];
                var departureDate = DateTime.Now.AddDays(-random.Next(1, 5));
                var returnDate = departureDate.AddDays(random.Next(2, 8));
                var daysCount = (int)(returnDate - departureDate).TotalDays;
                var remainingDays = (int)(returnDate - DateTime.Now).TotalDays;

                sampleData.Add(new ActiveDriverInfo
                {
                    DriverName = driver.Name,
                    DriverCode = driver.Code,
                    DriverPhone = driver.Phone,
                    MissionPurpose = samplePurposes[random.Next(samplePurposes.Length)],
                    DepartureDate = departureDate,
                    ReturnDate = returnDate,
                    DaysCount = daysCount,
                    RemainingDays = remainingDays,
                    ProjectsText = sampleProjects[random.Next(sampleProjects.Length)],
                    VisitorsText = $"المهندس {driver.Name.Split(' ')[0]} والفريق المرافق",
                    Status = remainingDays <= 0 ? "منتهي" : remainingDays <= 1 ? "ينتهي اليوم" : remainingDays <= 2 ? "ينتهي غداً" : "نشط",
                    StatusColor = remainingDays <= 0 ? "#F44336" : remainingDays <= 1 ? "#FF9800" : remainingDays <= 2 ? "#FF5722" : "#4CAF50",
                    DepartureDateText = $"من: {departureDate:dd/MM/yyyy}",
                    ReturnDateText = $"إلى: {returnDate:dd/MM/yyyy}",
                    RemainingDaysText = remainingDays <= 0 ? "انتهت" : $"{remainingDays} يوم متبقي"
                });
            }

            return sampleData;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
