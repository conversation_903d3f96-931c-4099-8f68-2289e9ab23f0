<Window x:Class="DriverManagementSystem.Views.QuickAccessWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الوصول السريع - السائقين المتواجدين بالميدان"
        Height="800"
        Width="1200"
        MinHeight="600"
        MinWidth="900"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        ResizeMode="CanResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft"
        FontFamily="Arial">

    <Window.Resources>
        <!-- تحويل Boolean إلى Visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- ستايل البطاقات -->
        <Style x:Key="DriverCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#2196F3" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- ستايل الإحصائيات -->
        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.15" BlurRadius="4"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- الهيدر -->
            <RowDefinition Height="Auto"/> <!-- الإحصائيات -->
            <RowDefinition Height="Auto"/> <!-- الفلاتر -->
            <RowDefinition Height="*"/>   <!-- المحتوى -->
            <RowDefinition Height="Auto"/> <!-- الفوتر -->
        </Grid.RowDefinitions>

        <!-- الهيدر -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="الوصول السريع" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               Margin="0,0,0,5"/>
                    <TextBlock Text="السائقين المتواجدين بالميدان حالياً" 
                               FontSize="14" 
                               Foreground="#E3F2FD"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🔄 تحديث" 
                            Command="{Binding RefreshCommand}"
                            Background="#1976D2" 
                            Foreground="White" 
                            BorderThickness="0" 
                            Padding="15,8" 
                            Margin="5,0"
                            FontSize="12"
                            Cursor="Hand"/>
                    
                    <TextBlock Text="{Binding LastUpdateTime, StringFormat='آخر تحديث: {0}'}" 
                               Foreground="#E3F2FD" 
                               FontSize="11" 
                               VerticalAlignment="Center" 
                               Margin="10,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Border Grid.Row="1" Background="#FAFAFA" Padding="20,15">
            <UniformGrid Rows="1" Columns="5">
                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="{Binding Statistics.TotalActiveDrivers}" 
                                   FontSize="20" 
                                   FontWeight="Bold" 
                                   Foreground="#2196F3" 
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="سائق نشط" 
                                   FontSize="11" 
                                   Foreground="#666" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="{Binding Statistics.DriversInField}" 
                                   FontSize="20" 
                                   FontWeight="Bold" 
                                   Foreground="#4CAF50" 
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="في الميدان" 
                                   FontSize="11" 
                                   Foreground="#666" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="{Binding Statistics.ActiveVisits}" 
                                   FontSize="20" 
                                   FontWeight="Bold" 
                                   Foreground="#FF9800" 
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="زيارة نشطة" 
                                   FontSize="11" 
                                   Foreground="#666" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="{Binding Statistics.EndingToday}" 
                                   FontSize="20" 
                                   FontWeight="Bold" 
                                   Foreground="#F44336" 
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="ينتهي اليوم" 
                                   FontSize="11" 
                                   Foreground="#666" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="{Binding Statistics.EndingTomorrow}" 
                                   FontSize="20" 
                                   FontWeight="Bold" 
                                   Foreground="#9C27B0" 
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="ينتهي غداً" 
                                   FontSize="11" 
                                   Foreground="#666" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </UniformGrid>
        </Border>

        <!-- الفلاتر -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- البحث -->
                <TextBox Grid.Column="0" 
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         FontSize="12"
                         Padding="10,8"
                         BorderBrush="#DDD"
                         BorderThickness="1"
                         VerticalContentAlignment="Center">
                    <TextBox.Style>
                        <Style TargetType="TextBox">
                            <Style.Triggers>
                                <Trigger Property="Text" Value="">
                                    <Setter Property="Background">
                                        <Setter.Value>
                                            <VisualBrush AlignmentX="Right" AlignmentY="Center" Stretch="None">
                                                <VisualBrush.Visual>
                                                    <TextBlock Text="🔍 البحث في السائقين..." 
                                                               Foreground="#999" 
                                                               FontSize="12"/>
                                                </VisualBrush.Visual>
                                            </VisualBrush>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>

                <!-- الفلاتر -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                    <CheckBox Content="في الميدان فقط" 
                              IsChecked="{Binding ShowOnlyInField}" 
                              Margin="0,0,15,0" 
                              FontSize="12"/>
                    <CheckBox Content="ينتهي اليوم" 
                              IsChecked="{Binding ShowEndingToday}" 
                              Margin="0,0,15,0" 
                              FontSize="12"/>
                    <CheckBox Content="ينتهي غداً" 
                              IsChecked="{Binding ShowEndingTomorrow}" 
                              Margin="0,0,15,0" 
                              FontSize="12"/>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="مسح الفلاتر" 
                            Command="{Binding ClearFiltersCommand}"
                            Background="#FFF" 
                            BorderBrush="#DDD" 
                            Padding="12,6" 
                            Margin="5,0" 
                            FontSize="11"/>
                    <Button Content="تحديد الكل" 
                            Command="{Binding SelectAllCommand}"
                            Background="#FFF" 
                            BorderBrush="#DDD" 
                            Padding="12,6" 
                            Margin="5,0" 
                            FontSize="11"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="3">
            <!-- مؤشر التحميل -->
            <Border Background="White" 
                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="🔄" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="جاري تحميل البيانات..." 
                               FontSize="16" 
                               Foreground="#666" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- قائمة السائقين -->
            <ScrollViewer VerticalScrollBarVisibility="Auto" 
                          HorizontalScrollBarVisibility="Disabled"
                          Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <ScrollViewer.Style>
                    <Style TargetType="ScrollViewer">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ScrollViewer.Style>
                
                <ItemsControl ItemsSource="{Binding FilteredDrivers}" Margin="10">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>

                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Style="{StaticResource DriverCardStyle}" Width="350" Height="280">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- هيدر البطاقة -->
                                    <Grid Grid.Row="0" Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding DriverName}"
                                                       FontSize="16"
                                                       FontWeight="Bold"
                                                       Foreground="#333"
                                                       TextTrimming="CharacterEllipsis"/>
                                            <TextBlock Text="{Binding DriverCode, StringFormat='الكود: {0}'}"
                                                       FontSize="11"
                                                       Foreground="#666"
                                                       Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <CheckBox Grid.Column="1"
                                                  IsChecked="{Binding IsSelected}"
                                                  VerticalAlignment="Top"/>
                                    </Grid>

                                    <!-- معلومات التكليف -->
                                    <Border Grid.Row="1"
                                            Background="#F8F9FA"
                                            CornerRadius="4"
                                            Padding="10"
                                            Margin="0,0,0,12">
                                        <StackPanel>
                                            <TextBlock Text="{Binding VisitNumber, StringFormat='زيارة رقم: {0}'}"
                                                       FontSize="12"
                                                       FontWeight="SemiBold"
                                                       Foreground="#2196F3"
                                                       Margin="0,0,0,4"/>
                                            <TextBlock Text="{Binding DateRangeText}"
                                                       FontSize="11"
                                                       Foreground="#666"
                                                       Margin="0,0,0,4"/>
                                            <Border Background="{Binding StatusColor}"
                                                    CornerRadius="10"
                                                    Padding="8,2"
                                                    HorizontalAlignment="Right">
                                                <TextBlock Text="{Binding Status}"
                                                           FontSize="10"
                                                           Foreground="White"
                                                           FontWeight="SemiBold"/>
                                            </Border>
                                        </StackPanel>
                                    </Border>

                                    <!-- تفاصيل المهمة -->
                                    <StackPanel Grid.Row="2" Margin="0,0,0,12">
                                        <TextBlock Text="الغرض:"
                                                   FontSize="11"
                                                   FontWeight="SemiBold"
                                                   Foreground="#333"
                                                   Margin="0,0,0,2"/>
                                        <TextBlock Text="{Binding MissionPurpose}"
                                                   FontSize="10"
                                                   Foreground="#666"
                                                   TextWrapping="Wrap"
                                                   MaxHeight="40"
                                                   TextTrimming="CharacterEllipsis"
                                                   Margin="0,0,0,8"/>

                                        <TextBlock Text="المشاريع:"
                                                   FontSize="11"
                                                   FontWeight="SemiBold"
                                                   Foreground="#333"
                                                   Margin="0,0,0,2"/>
                                        <TextBlock Text="{Binding ProjectsText}"
                                                   FontSize="10"
                                                   Foreground="#666"
                                                   TextWrapping="Wrap"
                                                   MaxHeight="30"
                                                   TextTrimming="CharacterEllipsis"
                                                   Margin="0,0,0,8"/>

                                        <TextBlock Text="القائمين بالزيارة:"
                                                   FontSize="11"
                                                   FontWeight="SemiBold"
                                                   Foreground="#333"
                                                   Margin="0,0,0,2"/>
                                        <TextBlock Text="{Binding VisitorsText}"
                                                   FontSize="10"
                                                   Foreground="#666"
                                                   TextWrapping="Wrap"
                                                   MaxHeight="30"
                                                   TextTrimming="CharacterEllipsis"/>
                                    </StackPanel>

                                    <!-- فوتر البطاقة -->
                                    <Grid Grid.Row="3">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding PhoneNumber, StringFormat='📱 {0}'}"
                                                       FontSize="10"
                                                       Foreground="#666"/>
                                            <TextBlock Text="{Binding VehicleType, StringFormat='🚗 {0}'}"
                                                       FontSize="10"
                                                       Foreground="#666"
                                                       Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <Border Grid.Column="1"
                                                Background="#FFF3E0"
                                                CornerRadius="4"
                                                Padding="6,3">
                                            <TextBlock Text="{Binding RemainingDaysText}"
                                                       FontSize="10"
                                                       FontWeight="SemiBold"
                                                       Foreground="#FF9800"/>
                                        </Border>
                                    </Grid>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Grid>

        <!-- الفوتر -->
        <Border Grid.Row="4" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                           Text="{Binding FilteredDrivers.Count, StringFormat='عدد السائقين المعروضين: {0}'}" 
                           FontSize="12" 
                           Foreground="#666" 
                           VerticalAlignment="Center"/>

                <Button Grid.Column="1" 
                        Content="إغلاق" 
                        Click="CloseButton_Click"
                        Background="#F44336" 
                        Foreground="White" 
                        BorderThickness="0" 
                        Padding="20,8" 
                        FontSize="12"/>
            </Grid>
        </Border>
    </Grid>
</Window>
