using System;
using System.Windows;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة الوصول السريع - عرض السائقين المتواجدين بالميدان
    /// </summary>
    public partial class QuickAccessWindow : Window
    {
        private readonly QuickAccessViewModel _viewModel;

        public QuickAccessWindow()
        {
            InitializeComponent();
            
            _viewModel = new QuickAccessViewModel();
            DataContext = _viewModel;

            // إعداد النافذة
            SetupWindow();
            
            System.Diagnostics.Debug.WriteLine("🚀 تم فتح نافذة الوصول السريع");
        }

        /// <summary>
        /// إعداد خصائص النافذة
        /// </summary>
        private void SetupWindow()
        {
            try
            {
                // توسيط النافذة
                WindowStartupLocation = WindowStartupLocation.CenterScreen;
                
                // إعداد الحد الأدنى للحجم
                MinWidth = 900;
                MinHeight = 600;
                
                // إعداد الأيقونة إذا كانت متوفرة
                try
                {
                    Icon = new System.Windows.Media.Imaging.BitmapImage(
                        new Uri("pack://application:,,,/Resources/quick-access-icon.ico"));
                }
                catch
                {
                    // تجاهل خطأ الأيقونة إذا لم تكن موجودة
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إعداد نافذة الوصول السريع بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔒 إغلاق نافذة الوصول السريع");
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة لتنظيف الموارد
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // تنظيف الموارد
                _viewModel?.Dispose();
                
                System.Diagnostics.Debug.WriteLine("🧹 تم تنظيف موارد نافذة الوصول السريع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف الموارد: {ex.Message}");
            }
            finally
            {
                base.OnClosed(e);
            }
        }

        /// <summary>
        /// معالج تحميل النافذة
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // يمكن إضافة أي إعدادات إضافية هنا
                System.Diagnostics.Debug.WriteLine("📋 تم تحميل نافذة الوصول السريع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج تغيير حجم النافذة
        /// </summary>
        private void Window_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                // يمكن إضافة منطق تكيف التخطيط مع حجم النافذة هنا
                if (e.NewSize.Width < 1000)
                {
                    // تقليل عدد الأعمدة في الشبكة للشاشات الصغيرة
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالج تغيير الحجم: {ex.Message}");
            }
        }
    }
}
